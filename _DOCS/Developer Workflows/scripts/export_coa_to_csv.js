function exportCOAToCSV() {
  // First, get all chart of account companies
  databaseConnection.obj.getAll('chart_of_accounts_companies', {archived: false}, function(companies) {
    console.log(`Processing ${companies.length} chart of account companies...`);

    // Create CSV header
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "ID,Object UID,Name,Account ID,QuickBooks Account ID,COA Company\n";

    let allChartOfAccounts = [];
    let processedCompanies = 0;

    // Process each company to get its chart of accounts
    companies.forEach(function(company) {
      const queryObj = {
        objectType: "chart_of_accounts",
        queryObj: {chart_of_accounts_company: company.id},
        getChildObjs: 0,
        getJust: {}
      };

      databaseConnection.obj.get(queryObj, function(chartOfAccounts) {
        console.log(`Found ${chartOfAccounts.length} chart of accounts for company: ${company.name}`);

        // Add company info to each chart of account
        chartOfAccounts.forEach(function(coa) {
          coa.companyInfo = `${company.id} - ${company.name}`;
        });

        // Add to master list
        allChartOfAccounts = allChartOfAccounts.concat(chartOfAccounts);
        processedCompanies++;

        // Check if all companies have been processed
        if (processedCompanies === companies.length) {
          console.log(`Total chart of accounts found: ${allChartOfAccounts.length}`);
          processAllCOAs();
        }
      });
    });

    function processAllCOAs() {
      // Process chart of accounts in batches
      const batchSize = 100;
      let currentIndex = 0;

      function processBatch() {
        const endIndex = Math.min(currentIndex + batchSize, allChartOfAccounts.length);

        // Process current batch
        for (let i = currentIndex; i < endIndex; i++) {
          const coa = allChartOfAccounts[i];

          // Escape quotes in fields
          const escapedName = coa.name ? coa.name.replace(/"/g, '""') : '';
          const escapedCompanyInfo = coa.companyInfo ? coa.companyInfo.replace(/"/g, '""') : '';
          const escapedAccountId = coa.account_id ? coa.account_id.replace(/"/g, '""') : '';
          const escapedQBAccountId = coa.quickbooks_account_id ? coa.quickbooks_account_id.replace(/"/g, '""') : '';

          csvContent += `${coa.id},"${coa.obj_uid || ''}","${escapedName}","${escapedAccountId}","${escapedQBAccountId}","${escapedCompanyInfo}"\n`;
        }

        currentIndex = endIndex;

        // Show progress
        console.log(`Processed ${currentIndex}/${allChartOfAccounts.length} chart of accounts`);

        // Continue with next batch or finish
        if (currentIndex < allChartOfAccounts.length) {
          setTimeout(processBatch, 0);
        } else {
          // All COAs processed, create download
          const encodedUri = encodeURI(csvContent);
          const link = document.createElement("a");
          link.setAttribute("href", encodedUri);
          link.setAttribute("download", "Infinity Hospitality COAs.csv");
          document.body.appendChild(link);

          // Trigger download
          link.click();

          // Clean up
          document.body.removeChild(link);

          console.log(`Exported ${allChartOfAccounts.length} chart of accounts to CSV`);
        }
      }

      // Start processing
      processBatch();
    }
  });
}

// Run the function
exportCOAToCSV();
