function exportCOAToCSV() {
  // First, get all chart of account companies
  databaseConnection.obj.getAll('chart_of_accounts_companies', function(companies) {
    console.log(`Processing ${companies.length} chart of account companies...`);

    // Create CSV header
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "ID,Name,Account ID,QuickBooks Account ID,COA Company ID,COA Company Name\n";

    // Create HTML table for display with alternating company colors
    let htmlContent = `
      <style>
        .coa-table { border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; }
        .coa-table th, .coa-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .coa-table th { background-color: #f2f2f2; font-weight: bold; }
        .company-group-0 { background-color: #f9f9f9; }
        .company-group-1 { background-color: #ffffff; }
      </style>
      <table class="coa-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Account ID</th>
            <th>QuickBooks Account ID</th>
            <th>COA Company ID</th>
            <th>COA Company Name</th>
          </tr>
        </thead>
        <tbody>
    `;

    let allChartOfAccounts = [];
    let processedCompanies = 0;

    // Process each company to get its chart of accounts
    companies.forEach(function(company) {

      databaseConnection.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: company.id}, function(chartOfAccounts) {
        console.log(`Found ${chartOfAccounts.length} chart of accounts for company: ${company.name}`);

        // Add company info to each chart of account
        chartOfAccounts.forEach(function(coa) {
          coa.companyId = company.id;
          coa.companyName = company.name;
        });

        // Add to master list
        allChartOfAccounts = allChartOfAccounts.concat(chartOfAccounts);
        processedCompanies++;

        // Check if all companies have been processed
        if (processedCompanies === companies.length) {
          console.log(`Total chart of accounts found: ${allChartOfAccounts.length}`);
          processAllCOAs();
        }
      });
    });

    function processAllCOAs() {
      // Sort by company ID to group together
      allChartOfAccounts.sort((a, b) => a.companyId - b.companyId);

      // Process chart of accounts in batches
      const batchSize = 100;
      let currentIndex = 0;
      let currentCompanyId = null;
      let companyGroupIndex = 0;

      function processBatch() {
        const endIndex = Math.min(currentIndex + batchSize, allChartOfAccounts.length);

        // Process current batch
        for (let i = currentIndex; i < endIndex; i++) {
          const coa = allChartOfAccounts[i];

          // Check if we've moved to a new company
          if (currentCompanyId !== coa.companyId) {
            currentCompanyId = coa.companyId;
            companyGroupIndex = (companyGroupIndex + 1) % 2;
          }

          // Escape quotes in fields
          const escapedName = coa.name ? coa.name.replace(/"/g, '""') : '';
          const escapedCompanyName = coa.companyName ? coa.companyName.replace(/"/g, '""') : '';
          const escapedAccountId = coa.account_id ? coa.account_id.replace(/"/g, '""') : '';
          const escapedQBAccountId = coa.quickbooks_account_id ? coa.quickbooks_account_id.replace(/"/g, '""') : '';

          // Add to CSV
          csvContent += `${coa.id},"${escapedName}","${escapedAccountId}","${escapedQBAccountId}",${coa.companyId},"${escapedCompanyName}"\n`;

          // Add to HTML table with alternating company colors
          const escapedNameHtml = coa.name || '';
          const escapedCompanyNameHtml = coa.companyName || '';
          const escapedAccountIdHtml = coa.account_id || '';
          const escapedQBAccountIdHtml = coa.quickbooks_account_id || '';

          htmlContent += `
            <tr class="company-group-${companyGroupIndex}">
              <td>${coa.id}</td>
              <td>${escapedNameHtml}</td>
              <td>${escapedAccountIdHtml}</td>
              <td>${escapedQBAccountIdHtml}</td>
              <td>${coa.companyId}</td>
              <td>${escapedCompanyNameHtml}</td>
            </tr>
          `;
        }

        currentIndex = endIndex;

        // Show progress
        console.log(`Processed ${currentIndex}/${allChartOfAccounts.length} chart of accounts`);

        // Continue with next batch or finish
        if (currentIndex < allChartOfAccounts.length) {
          setTimeout(processBatch, 0);
        } else {
          // Close HTML table
          htmlContent += `
            </tbody>
          </table>
          <br>
          <button onclick="downloadCSV()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">Download CSV</button>
          `;

          // Create a new window/tab to display the HTML table
          const newWindow = window.open('', '_blank');
          newWindow.document.write(`
            <html>
              <head>
                <title>Infinity Hospitality COAs</title>
              </head>
              <body>
                <h2>Infinity Hospitality Chart of Accounts</h2>
                ${htmlContent}
                <script>
                  function downloadCSV() {
                    const csvContent = \`${csvContent.replace(/`/g, '\\`')}\`;
                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute("download", "Infinity Hospitality COAs.csv");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                </script>
              </body>
            </html>
          `);
          newWindow.document.close();

          console.log(`Displayed ${allChartOfAccounts.length} chart of accounts with alternating company colors`);
        }
      }

      // Start processing
      processBatch();
    }
  });
}

// Run the function
exportCOAToCSV();
